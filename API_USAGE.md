# 绿盟原理漏洞API使用说明

## 概述

这是一个简化的HTTP API服务器，专门用于获取绿盟扫描系统中的原理漏洞信息。服务器启动时会自动登录绿盟系统，无需手动登录操作。

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务器

```bash
# 使用启动脚本（推荐）
python start_server.py

# 或直接启动
python vuln_api_server.py --host ************ --port 5000
```

### 3. 测试API

```bash
# 运行客户端示例
python api_client_example.py
```

## API接口

服务器提供以下2个简单的API接口：

### 1. 获取服务器信息

```
GET /
```

**响应示例：**
```json
{
    "message": "绿盟原理漏洞API服务器",
    "version": "1.0.0",
    "status": "已登录",
    "login_time": "2024-01-01T10:00:00",
    "login_duration": "3600秒",
    "login_timeout": "3600秒",
    "target_host": "************",
    "endpoints": {
        "GET /": "服务器信息和状态",
        "GET /principle-vulns": "获取所有已完成任务的原理扫描漏洞"
    },
    "features": [
        "自动登录和会话管理",
        "登录状态过期自动重新登录",
        "专注原理扫描漏洞分析",
        "详细的漏洞统计信息"
    ]
}
```

### 2. 获取所有原理扫描漏洞

```
GET /principle-vulns
```

**响应示例：**
```json
{
    "success": true,
    "message": "成功获取 3 个任务的原理扫描漏洞",
    "data": {
        "task_count": 3,
        "task_ids": ["12345", "12346", "12347"],
        "vulns": [
            {
                "vuln_id": "V001",
                "i18n_name": "【原理扫描】SQL注入漏洞",
                "vuln_level": "high",
                "severity_points": 8.5,
                "target": "*************:80"
            }
        ],
        "total_count": 15,
        "statistics": {
            "total_count": 15,
            "level_count": {
                "high": 5,
                "medium": 8,
                "low": 2
            },
            "severity_distribution": {
                "high": 5,
                "medium": 8,
                "low": 2
            }
        }
    }
}
```

### 3. 获取指定任务的原理扫描漏洞

```
GET /principle-vulns/<task_id>
```

**参数：**
- `task_id`: 任务ID

**响应示例：**
```json
{
    "success": true,
    "message": "成功获取任务 12345 的原理扫描漏洞",
    "data": {
        "task_id": "12345",
        "vulns": [
            {
                "vuln_id": "V001",
                "i18n_name": "【原理扫描】SQL注入漏洞",
                "vuln_level": "high",
                "severity_points": 8.5,
                "target": "*************:80"
            }
        ],
        "total_count": 5,
        "statistics": {
            "total_count": 5,
            "level_count": {
                "high": 2,
                "medium": 2,
                "low": 1
            }
        }
    }
}
```

## 使用示例

### Python客户端

```python
import requests

# 1. 获取所有原理扫描漏洞
response = requests.get('http://localhost:5000/principle-vulns')
data = response.json()

if data['success']:
    vulns = data['data']['vulns']
    print(f"发现 {len(vulns)} 个原理扫描漏洞")
    
    for vuln in vulns[:5]:  # 显示前5个
        print(f"- {vuln['i18n_name']} ({vuln['vuln_level']})")

# 2. 获取指定任务的原理扫描漏洞
task_id = "12345"
response = requests.get(f'http://localhost:5000/principle-vulns/{task_id}')
data = response.json()

if data['success']:
    vulns = data['data']['vulns']
    print(f"任务 {task_id} 发现 {len(vulns)} 个原理扫描漏洞")
```

### curl命令

```bash
# 获取服务器信息
curl http://localhost:5000/

# 获取所有原理扫描漏洞
curl http://localhost:5000/principle-vulns

# 获取指定任务的原理扫描漏洞
curl http://localhost:5000/principle-vulns/12345
```

## 错误处理

当系统未登录或发生错误时，API会返回相应的错误信息：

```json
{
    "success": false,
    "message": "系统未登录，请稍后重试",
    "data": []
}
```

常见错误码：
- `503`: 系统未登录
- `500`: 服务器内部错误
- `404`: 任务不存在

## 注意事项

1. **自动登录**: 服务器启动时会自动登录绿盟系统，无需手动操作
2. **原理扫描**: 只返回包含"原理扫描"标识的漏洞
3. **已完成任务**: 只分析状态为"已完成"的任务
4. **跨域支持**: 支持CORS，可与前端应用集成
5. **JSON格式**: 所有响应都是JSON格式

## 故障排除

1. **服务器启动失败**: 检查依赖包是否安装完整
2. **登录失败**: 检查绿盟服务器地址是否正确
3. **无法获取漏洞**: 确认有已完成的扫描任务
4. **端口冲突**: 修改启动端口或关闭占用端口的程序
