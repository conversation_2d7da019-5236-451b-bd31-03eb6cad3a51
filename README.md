# 绿盟原理漏洞API服务器

一个简化的HTTP API服务器，专门用于获取绿盟扫描系统中的原理漏洞信息。服务器启动时自动登录，提供简单的RESTful API接口。

## 功能特性

- 🚀 **一键启动**: 服务器启动时自动登录绿盟系统，无需手动操作
- 🎯 **专注原理漏洞**: 专门获取和分析原理扫描漏洞
- 🌐 **简单API**: 仅提供3个核心API接口，使用简单
- 📊 **统计分析**: 自动统计漏洞分布和风险等级
- 🔄 **跨域支持**: 支持CORS，可与前端应用集成
- 📋 **JSON格式**: 所有响应都是标准JSON格式

## 项目结构

```
.
├── README.md               # 项目说明文档
├── requirements.txt        # 依赖包列表
├── example.py              # 完整使用示例
├── login_manager.py        # 登录管理模块
├── task_manager.py         # 任务管理模块
├── vuln_manager.py         # 漏洞管理模块
├── vuln_api_server.py      # HTTP API服务器
├── api_client_example.py   # API客户端示例
└── start_server.py         # 服务器启动脚本
```

## 环境要求

- Python 3.6+
- 依赖包：
  - `requests` - HTTP请求库
  - `ddddocr` - 验证码识别库
  - `flask` - Web框架
  - `flask-cors` - 跨域支持

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install requests ddddocr flask flask-cors
```

## 快速开始

### 1. 基本使用

```python
from login_manager import GreenLeagueLogin
from task_manager import TaskManager
from vuln_manager import VulnManager

# 登录系统
login_manager = GreenLeagueLogin(host="************")
success, login_result = login_manager.auto_login()

if success:
    # 初始化管理器
    session = login_manager.get_session()
    base_url = login_manager.get_base_url()
    
    task_manager = TaskManager(session, base_url)
    vuln_manager = VulnManager(session, base_url)
    
    # 获取已完成的任务
    success, completed_tasks = task_manager.get_completed_tasks()
    
    # 分析漏洞
    if success and completed_tasks:
        task_ids = [task['task_id'] for task in completed_tasks]
        analysis_result = vuln_manager.analyze_multiple_tasks_vulns(task_ids)
```

### 2. 运行示例程序

```bash
python example.py
```

### 3. 启动API服务器

```bash
# 使用启动脚本（推荐）
python start_server.py

# 或直接启动
python vuln_api_server.py --host ************ --port 5000
```

服务器启动后会自动登录绿盟系统，API接口地址：`http://localhost:5000`

### 4. 测试API

```bash
# 运行客户端示例
python api_client_example.py

# 或使用curl测试
curl http://localhost:5000/principle-vulns
```

## 模块说明

### LoginManager (登录管理)

负责处理系统登录相关功能：

- 验证码获取和自动识别
- 用户认证和会话管理
- SSL证书处理

**主要方法：**
- `auto_login()` - 自动登录
- `get_session()` - 获取认证会话
- `get_base_url()` - 获取基础URL

### TaskManager (任务管理)

负责扫描任务的查询和管理：

- 任务列表查询
- 任务状态过滤
- 任务统计分析

**主要方法：**
- `get_completed_tasks()` - 获取已完成任务
- `get_task_statistics()` - 获取任务统计
- `search_tasks_by_name()` - 按名称搜索任务
- `display_task_summary()` - 显示任务摘要

### VulnManager (漏洞管理)

负责漏洞信息的查询和分析：

- 漏洞分布查询
- 原理扫描漏洞过滤
- 漏洞统计分析

**主要方法：**
- `get_vuln_distribution()` - 获取漏洞分布
- `filter_principle_scan_vulns()` - 过滤原理扫描漏洞
- `analyze_multiple_tasks_vulns()` - 分析多任务漏洞
- `display_vuln_statistics()` - 显示漏洞统计

### HTTP API接口

提供3个简单的RESTful API接口：

**核心接口：**
- `GET /` - 获取服务器信息和状态
- `GET /principle-vulns` - 获取所有已完成任务的原理扫描漏洞
- `GET /principle-vulns/<task_id>` - 获取指定任务的原理扫描漏洞

## API使用示例

### Python客户端

```python
import requests

# 1. 获取所有原理扫描漏洞
response = requests.get('http://localhost:5000/principle-vulns')
data = response.json()

if data['success']:
    vulns = data['data']['vulns']
    print(f"发现 {len(vulns)} 个原理扫描漏洞")

    for vuln in vulns[:5]:  # 显示前5个
        print(f"- {vuln['i18n_name']} ({vuln['vuln_level']})")

# 2. 获取指定任务的原理扫描漏洞
task_id = "12345"
response = requests.get(f'http://localhost:5000/principle-vulns/{task_id}')
data = response.json()

if data['success']:
    vulns = data['data']['vulns']
    print(f"任务 {task_id} 发现 {len(vulns)} 个原理扫描漏洞")
```

### curl命令

```bash
# 获取服务器信息
curl http://localhost:5000/

# 获取所有原理扫描漏洞
curl http://localhost:5000/principle-vulns

# 获取指定任务的原理扫描漏洞
curl http://localhost:5000/principle-vulns/12345
```

## 配置说明

### 默认配置

- 服务器地址: `************`
- 协议: HTTPS
- SSL验证: 已禁用（适用于自签名证书）

### 自定义配置

```python
# 使用自定义服务器地址
login_manager = GreenLeagueLogin(host="your-server-ip")
```

## 使用示例

### 完整工作流程

```python
def complete_workflow():
    # 1. 登录
    login_manager = GreenLeagueLogin(host="************")
    success, _ = login_manager.auto_login()
    
    if not success:
        return
    
    # 2. 初始化管理器
    session = login_manager.get_session()
    base_url = login_manager.get_base_url()
    task_manager = TaskManager(session, base_url)
    vuln_manager = VulnManager(session, base_url)
    
    # 3. 获取任务统计
    task_manager.get_task_statistics()
    
    # 4. 获取已完成任务
    success, completed_tasks = task_manager.get_completed_tasks()
    
    # 5. 分析漏洞
    if success and completed_tasks:
        task_ids = [task['task_id'] for task in completed_tasks]
        analysis_result = vuln_manager.analyze_multiple_tasks_vulns(
            task_ids, focus_on_principle=True
        )
        
        # 6. 显示结果
        all_vulns = analysis_result['all_vulns']
        if all_vulns:
            vuln_manager.display_vuln_details(all_vulns[:10])
            vuln_manager.display_vuln_statistics(all_vulns)
```

## 注意事项

1. **SSL证书**: 系统默认禁用SSL证书验证，适用于使用自签名证书的服务器
2. **验证码识别**: 使用ddddocr库进行验证码自动识别，准确率较高
3. **会话管理**: 登录后的会话会自动维护，无需手动处理Cookie
4. **错误处理**: 所有API调用都包含错误处理和重试机制

## 故障排除

### 常见问题

1. **登录失败**
   - 检查服务器地址是否正确
   - 确认网络连接正常
   - 验证用户名密码

2. **验证码识别失败**
   - 检查ddddocr库是否正确安装
   - 验证码图片质量问题

3. **API调用失败**
   - 检查会话是否有效
   - 确认API接口地址正确

## 许可证

本项目仅供学习和研究使用。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

---

## 快速体验

1. 安装依赖：`pip install -r requirements.txt`
2. 启动服务器：`python start_server.py`
3. 测试API：`python api_client_example.py`
4. 查看详细文档：[API_USAGE.md](API_USAGE.md)

**免责声明**: 本工具仅用于授权的安全测试和漏洞管理，请勿用于非法用途。
